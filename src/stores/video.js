import { ref, computed } from "vue";
import { useSimpleCustomFetch } from "../composables/useCustomFetch.js";

export const useVideoStore = defineStore("Video", () => {
  const isLoadingRef = ref(true);
  const videoDataListRef = ref([]);
  const existingVideoNamesListRef = ref([]);
  const hasReloadTextCheckRef = ref(false);

  // Computed properties
  const isLoading = computed(() => isLoadingRef.value);
  const videoDataList = computed(() => videoDataListRef.value);
  const existingVideoNamesList = computed(() => existingVideoNamesListRef.value);
  const hasReloadTextCheck = computed(() => hasReloadTextCheckRef.value);

  // Helper function to process video data
  const processVideoData = (data) => {
    if (!data || !Array.isArray(data)) return [];
    
    return data
      .map((item) => ({
        ...item,
        selectedText: "",
        link: item.link ? `https://${item.link}` : item.link,
      }))
      .reverse();
  };

  // Get video data from API
  const getVideoDataAsync = async () => {
    isLoadingRef.value = true;
    hasReloadTextCheckRef.value = false;
    
    try {
      const response = await useSimpleCustomFetch("", {}, "flite", "getVideos");
      const processedData = processVideoData(response || []);
      
      videoDataListRef.value = processedData;
      existingVideoNamesListRef.value = processedData.map((item) =>
        item?.name?.toLowerCase() || ""
      );
      
      return processedData;
    } catch (error) {
      console.error("[IQ][useVideoStore] Error in getVideoDataAsync", error);
      hasReloadTextCheckRef.value = true;
      throw error;
    } finally {
      isLoadingRef.value = false;
    }
  };

  // Upload video file
  const uploadVideoAsync = async (formData) => {
    try {
      const response = await useSimpleCustomFetch(
        "",
        {
          method: "POST",
          body: formData,
        },
        "flite",
        "getVideos"
      );
      
      return response;
    } catch (error) {
      console.error("[IQ][useVideoStore] Error in uploadVideoAsync", error);
      throw error;
    }
  };

  // Delete video
  const deleteVideoAsync = async (videoName) => {
    try {
      const response = await useSimpleCustomFetch(
        "",
        {
          method: "DELETE",
        },
        "flite",
        "getVideos",
        videoName
      );
      
      return response;
    } catch (error) {
      console.error("[IQ][useVideoStore] Error in deleteVideoAsync", error);
      throw error;
    }
  };

  // Update video selection state
  const updateVideoSelection = (videoName, selectedText) => {
    videoDataListRef.value = videoDataListRef.value.map((item) => ({
      ...item,
      selectedText: item.name === videoName ? selectedText : item.selectedText,
    }));
  };

  // Clear all video selections
  const clearVideoSelections = () => {
    videoDataListRef.value = videoDataListRef.value.map((item) => ({
      ...item,
      selectedText: "",
    }));
  };

  // Reset store state
  const resetStore = () => {
    isLoadingRef.value = false;
    videoDataListRef.value = [];
    existingVideoNamesListRef.value = [];
    hasReloadTextCheckRef.value = false;
  };

  // Check if video name already exists
  const isVideoNameExists = (videoName) => {
    return existingVideoNamesListRef.value.includes(videoName.toLowerCase());
  };

  return {
    // State
    isLoading,
    videoDataList,
    existingVideoNamesList,
    hasReloadTextCheck,

    // Actions
    getVideoDataAsync,
    uploadVideoAsync,
    deleteVideoAsync,
    updateVideoSelection,
    clearVideoSelections,
    resetStore,
    isVideoNameExists,
  };
});
