.roche-video {
  .simple-table-head-column {
    &-video-name {
      width: 28%;
    }
    &-cloudfront-link {
      width: 30%;
    }
    &-size {
      width: 10%;
    }
    &-added {
      width: 18%;
    }
  }
  &-upload-label {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 42px;
    width: 190px;
  }
  &-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
  }
  &-publisher-form img {
    min-width: 25px;
  }
  &-cloudfront-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 50px;
  }
  &-copy-clipboard img {
    min-width: 18px;
  }
}