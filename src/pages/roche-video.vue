<template>
  <client-only>
    <content-wrapper :is-body-loading="isLoading">
      <block-wrapper is-transparent v-show="!isLoading">
        <template #title>
          <div class="display-1 color-green-dark">{{ $t("ROCHE.VIDEOS") }}</div>
          <div class="header-button">
            <button
              type="button"
              @click="uploadVideo($event)"
              v-if="!hasReloadTextCheck"
              class="btn-green padding-zero"
            >
              <label for="file" class="roche-video-upload-label">{{
                $t("ROCHE.NEW_VIDEO")
              }}</label>
              <input
                id="file"
                type="file"
                class="visibility-hidden display-none"
                title="NEW VIDEO"
                @change="checkUploadedVideoFileAsync"
                accept=".mp4"
              />
            </button>
            <div
              v-if="!hasReloadTextCheck"
              class="text-light-h3 color-grey font-italic"
            >
              {{ $t("ROCHE.MAX_SIZE_FORMAT") }}
            </div>
          </div>
        </template>
        <div class="roche-video-main-table">
          <simple-table
            v-if="videoData.length"
            :column-names="columnNames"
            :column-keys="columnKeys"
            :data-source="videoData"
            table-class="roche-video"
          >
            <template v-slot:videoName="props">
              <div class="roche-video-container">
                <div class="video-name-text font-bold text-h3">
                  {{ props?.data?.name ?? "" }}
                </div>
                <div class="roche-video-publisher-form">
                  <button
                    type="button"
                    @click="playVideo(props.data.link)"
                    class="btn-reset simple-data-tooltip"
                    :data-tooltip-text="playButtonTooltip"
                  >
                    <img
                      src="~/assets/images/circle-play.svg?skipsvgo=true"
                      alt="Play Video"
                    />
                  </button>
                </div>
              </div>
            </template>

            <template v-slot:cloudfrontLink="props">
              <div class="roche-video-cloudfront-row">
                <div class="color-navy-blue text-h4 cursor-pointer">
                  <span
                    class="text"
                    :id="`cloudfrontID${videoData.indexOf(props.data)}`"
                    @click="openVideoTab(props.data)"
                    >{{
                      props?.data?.link ?? ""
                    }}</span
                  >
                </div>
                <div class="roche-video-copy-clipboard">
                  <button
                    type="button"
                    :data-tooltip-text="copyLinkTooltip"
                    class="btn-reset simple-data-tooltip"
                    @click="copyVideoCloudfront(videoData.indexOf(props.data))"
                  >
                    <img src="~/assets/images/copy-icon.png" alt="copy" />
                  </button>
                </div>
              </div>
            </template>

            <template v-slot:size="props">
              <span class="text-light-h3 color-gunmetal-grey">
                {{ bytesToMegabytes(props.data?.size) ?? "" }}
              </span>
            </template>

            <template v-slot:added="props">
              <span class="text-light-h3 color-gunmetal-grey">
                {{ convertTimestampToCustomFormat(props.data?.createDate) ?? "" }}
              </span>
            </template>
            <template v-slot:actions="props">
              <simple-actions
                :is-edit-btn-displayed="false"
                @deleteOnClick="deleteVideo(props.data.name)"
              ></simple-actions>
            </template>
          </simple-table>
        </div>
        <noResultFound
          v-if="!videoData.length"
          :description="
            hasReloadTextCheck
              ? $t('COMMON.RELOAD_PAGE')
              : $t('NO_RESULT.VIDEO')
          "
        />
      </block-wrapper>
    </content-wrapper>
  </client-only>
</template>
<script setup>
import { ref, onMounted, onBeforeUnmount, getCurrentInstance } from "vue";
import { useStore } from "vuex";
import { useVideoStore } from "~/stores/video.js";
import { storeToRefs } from "pinia";
import recipeVideo from "@/components/recipeVideo";
import deletingModal from "@/components/deleting-modal";
import deleteModal from "@/components/delete-modal";
import invalidImageVideoPopup from "@/components/invalid-image-video-popup";
import sizeLimit from "@/components/size-limit.vue";
import ProcessModal from "../components/modals/process-modal.vue";
import noResultFound from "@/components/no-result-found";
import ContentWrapper from "@/components/content-wrapper/content-wrapper";
import BlockWrapper from "@/components/block-wrapper/block-wrapper";
import SimpleTable from "@/components/simple-table/simple-table";
import { useTimeUtils } from "~/composables/useTimeUtils";
import { useCommonUtils } from "~/composables/useCommonUtils";
import { useRefUtils } from "~/composables/useRefUtils";
import { useNuxtApp } from "#app";
import { useProjectLang } from "../composables/useProjectLang.js";
import { useBaseModal } from "~/composables/useBaseModal.js";
import { PROCESS_MODAL_TYPE } from "../models/process-modal.model.js";

const { convertTimestampToCustomFormat } = useTimeUtils();
const { $eventBus } = useNuxtApp();

const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const $t = instance.appContext.config.globalProperties.$t;
const store = useStore();

// Pinia store
const videoStore = useVideoStore();

const { getRef } = useRefUtils();
const { triggerLoading } = useCommonUtils();
const { readyProject } = useProjectLang();

// Modal configuration
const { openModal, closeModal: closeBaseModal } = useBaseModal({
  recipeVideo: recipeVideo,
  deletingModal: deletingModal,
  deleteModal: deleteModal,
  invalidImageVideoPopup: invalidImageVideoPopup,
  sizeLimit: sizeLimit,
  videoProcessModal: {
    component: ProcessModal,
    skipClickOutside: true,
    skipEscapeClick: true,
  },
});

const openVideo = ref(false);
// Use Pinia store state
const {
  videoDataList: videoData,
  hasReloadTextCheck,
  isLoading,
} = storeToRefs(videoStore);
// Modal state - keeping for backward compatibility with existing modal logic
const isDeletingModalVisible = ref(false);
const isDeleteRocheVideoModalOpen = ref(false);
const isInvalidVideoModalVisible = ref(false);
const isMaxVideoPopupVisible = ref(false);
const acceptedFile = ref("");
const videoResponseUrl = ref("");
const videoFile = ref([]);
const videoFilesName = ref([]);
const deleteName = ref("");
// existingVideoNamesList is accessed via videoStore.isVideoNameExists() method
const lang = ref("");
const playButtonTooltip = ref("Click to play video");
const copyLinkTooltip = ref("Copy link to clipboard");

// Table configuration
const columnNames = ref(["Video Name", "Cloudfront Link", "Size", "Added", ""]);
const columnKeys = ref([
  "videoName",
  "cloudfrontLink",
  "size",
  "added",
  "actions",
]);

onMounted(async () => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      document.addEventListener("keyup", handleESCClickOutside);
      lang.value = store.getters["userData/getDefaultLang"];
      await getVideoDataAsync();
      $eventBus.on("current-link", (link) => {
        if (link.includes("/roche-video")) {
          getVideoDataAsync();
        }
      });
    }
  });
});

onBeforeUnmount(() => {
  document.removeEventListener("keyup", handleESCClickOutside);
  $eventBus.off("current-link");
});

const handleESCClickOutside = (event) => {
  if (event?.key === "Escape") {
    closeModal();
  }
};

const playVideo = (data) => {
  openModal({
    name: "recipeVideo",
    props: {
      videoLink: data,
      closeModal: () => closeBaseModal("recipeVideo"),
    },
  });
};

const openVideoTab = (data) => {
  window.open(data.link, "_blank");
};

const bytesToMegabytes = (bytes) => {
  const megabytes = bytes / (1024 * 1024);
  return `${megabytes.toFixed(2)} MB`;
};

const getVideoDataAsync = async () => {
  try {
    await videoStore.getVideoDataAsync();
  } catch (e) {
    console.error(`${$keys.KEY_NAMES.ERROR_IN} getVideoDataAsync`, e);
  }
};

const copyVideoCloudfront = (index) => {
  const copyElement = getRef(`cloudfrontID${index}`);
  navigator.clipboard.writeText(copyElement.innerHTML);
  $eventBus.emit("copyToClipboard");
};

const deleteVideo = (name) => {
  deleteName.value = name;
  videoStore.updateVideoSelection(name, "DELETE");

  openModal({
    name: "deleteModal",
    props: {
      closeModal: () => closeBaseModal("deleteModal"),
      productInfoTitle: $t("DESCRIPTION_POPUP.DELETE_VIDEO"),
      productDescriptionOne: $t("DESCRIPTION_POPUP.DELETE_POPUP"),
      productDescriptionTwo: $t("DESCRIPTION_POPUP.VIDEO"),
      deleteItem: deleteRocheVideoAsync,
      availableLanguage: 0,
    },
  });
};

const deleteRocheVideoAsync = async () => {
  closeBaseModal("deleteModal");

  openModal({
    name: "deletingModal",
  });

  try {
    await videoStore.deleteVideoAsync(deleteName.value);
    triggerLoading($keys.KEY_NAMES.NEW_DELETED_SUCCESS);
    await getVideoDataAsync();
  } catch (e) {
    console.error(`${$keys.KEY_NAMES.ERROR_IN} deleteRocheVideoAsync`, e);
  } finally {
    closeBaseModal("deletingModal");
    closeModal();
  }
};

const resetFileInput = () => {
  const fileInput = document.getElementById("file");
  if (fileInput) {
    fileInput.value = ""; // Reset to empty string
  }
};

const checkUploadedVideoFileAsync = async (event) => {
  const files = event.target.files;
  videoFile.value = files.length > 0 ? files : null;

  if (videoFile?.value?.length > 0) {
    videoFilesName.value = videoFile.value[0].name
      .toLowerCase()
      .replaceAll(" ", "_");
    const reg = /(.*?)\.(mp4)$/;

    if (!videoFilesName.value.match(reg)) {
      acceptedFile.value = " mp4";
      openModal({
        name: "invalidImageVideoPopup",
        props: {
          closeModal: () => closeBaseModal("invalidImageVideoPopup"),
          acceptedFile: acceptedFile.value,
          video: true,
          image: false,
          zip: false,
        },
      });
      resetFileInput();
      return;
    }

    const fileSize = videoFile.value[0].size;
    if (fileSize > 250 * 1024 * 1024) {
      resetFileInput();
      openModal({
        name: "sizeLimit",
        props: {
          imageSizeAlert: "Your uploaded video size is larger than 250MB.",
          fileSizeAlert: "Max. size for video: 250MB",
          closeModal: () => closeBaseModal("sizeLimit"),
          isMaxVideoPopupVisible: true,
        },
      });
      return;
    } else if (videoStore.isVideoNameExists(videoFilesName.value)) {
      $eventBus.emit("videoNameExist");
      return;
    } else {
      openModal({
        name: "videoProcessModal",
        props: {
          modalType: PROCESS_MODAL_TYPE.UPLOADING,
        },
      });
      const payload = new FormData();
      payload.append("file", videoFile.value[0]);
      payload.append("name", videoFilesName.value);

      try {
        await videoStore.uploadVideoAsync(payload);
        triggerLoading($keys.KEY_NAMES.VIDEO_UPLOADED);
        await getVideoDataAsync();
        uploadVideo(event);
      } catch (error) {
        console.error(
          `${$keys.KEY_NAMES.ERROR_IN} checkUploadedVideoFileAsync`,
          error
        );
        triggerLoading($keys.KEY_NAMES.VIDEO_UNEXPECTED_ERROR);
      } finally {
        closeBaseModal("videoProcessModal");
      }
    }
  }
};

const uploadVideo = (event) => {
  event.target.value = "";
};

const closeModal = () => {
  videoStore.clearVideoSelections();

  // Close all modals
  closeBaseModal("recipeVideo");
  closeBaseModal("deleteModal");
  closeBaseModal("invalidImageVideoPopup");
  closeBaseModal("sizeLimit");
  closeBaseModal("deletingModal");
  closeBaseModal("videoProcessModal");

  openVideo.value = false;
  isDeleteRocheVideoModalOpen.value = false;
  isInvalidVideoModalVisible.value = false;
  isMaxVideoPopupVisible.value = false;
  isDeletingModalVisible.value = false;
  videoResponseUrl.value = "";
};
</script>
